<template>
    <el-dialog
    v-model="visible"
    :before-close="handleClose"
  >
    <el-row>
        <el-col :span="12">
            <el-form>
                <el-form-item label="站点名称:" prop="name">
                    <el-input v-model="form.name" placeholder="请输入站点名称"></el-input>
                </el-form-item>
            </el-form>
        </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import type {RowType} from "@/type/RowType"
    const props=defineProps({
        visible:{
            type:Boolean,
            default:false
        }
    })
    const form=reactive<RowType>({
        name:"",
        
    })

    const emit=defineEmits(["close"])
    const handleClose=()=>{
        emit("close")
    }

    const handleSave=()=>{
        console.log("保存")
        emit("close")
    }
</script>